<?php

declare(strict_types=1);

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// View language settings
return [
    'invalidCellMethod'     => '{class}::{method} is not a valid method.',
    'missingCellParameters' => '{class}::{method} has no params.',
    'invalidCellParameter'  => '"{0}" is not a valid param name.',
    'noCellClass'           => 'No view cell class provided.',
    'invalidCellClass'      => 'Unable to locate view cell class: "{0}".',
    'tagSyntaxError'        => 'You have a syntax error in your Parser tags: "{0}"',
    'invalidDecoratorClass' => '"{0}" is not a valid View Decorator.',
];
