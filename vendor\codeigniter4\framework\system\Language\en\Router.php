<?php

declare(strict_types=1);

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Router language settings
return [
    'invalidParameter'         => 'A parameter does not match the expected type.',
    'missingDefaultRoute'      => 'Unable to determine what should be displayed. A default route has not been specified in the routing file.',
    'invalidDynamicController' => 'A dynamic controller is not allowed for security reasons. Route handler: "{0}"',
    'invalidControllerName'    => 'The namespace delimiter is a backslash (\), not a slash (/). Route handler: "{0}"',
];
