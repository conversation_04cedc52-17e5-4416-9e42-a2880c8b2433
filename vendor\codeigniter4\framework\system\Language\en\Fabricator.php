<?php

declare(strict_types=1);

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Fabricator language settings
return [
    'invalidModel'      => 'Invalid model supplied for fabrication.',
    'missingFormatters' => 'No valid formatters defined.',
    'createFailed'      => 'Fabricator failed to insert on table "{0}": {1}',
];
