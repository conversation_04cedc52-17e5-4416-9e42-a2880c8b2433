<?php

namespace Faker\Provider\fr_FR;

class Color extends \Faker\Provider\Color
{
    protected static $safeColorNames = [
        'noir', 'marron', 'vert', 'marine', 'olive',
        'violet', 'turquoise', 'citron', 'bleu',
        'argenté', 'gris', 'jaune', 'fuchsia', 'blanc',
    ];

    /**
     * @source: https://fr.wikipedia.org/wiki/Liste_de_noms_de_couleur#Liste
     */
    protected static $allColorNames = [
        'Abri<PERSON>', 'Acajou', 'Aigue-marine', 'Amande', 'Amar<PERSON>', 'Ambre', 'Améthyste', 'Anthracite', 'Argent', 'Aubergine',
        'Au<PERSON>re', 'Avocat', 'Azur', '<PERSON><PERSON><PERSON>', '<PERSON>urre', 'B<PERSON>', '<PERSON>isque', '<PERSON>ist<PERSON>', '<PERSON>ume', 'Blanc cassé', '<PERSON> lunaire',
        '<PERSON><PERSON>', 'Bleu acier', 'Bleu barbeau', 'Bleu canard', 'Bleu céleste', '<PERSON>leu charrette', 'Bleu ciel', 'Bleu de cobalt',
        '<PERSON>le<PERSON> <PERSON>', 'Bleu électrique', 'Bleu givré', 'Bleu marine', 'Bleu nuit', 'Bleu outremer', 'Bleu paon', 'Bleu persan',
        'Bleu pétrole', 'Bleu roi', 'Bleu turquin', "Bouton d'or", 'Brique', 'Bronze', 'Brou de noix', "Caca d'oie", 'Cacao',
        'Cachou', 'Cæruleum', 'Café', 'Café au lait', 'Cannelle', 'Capucine', 'Caramel', 'Carmin', 'Carotte', 'Chamois', 'Chartreuse',
        'Chaudron', 'Chocolat', 'Cinabre', 'Citrouille', "Coquille d'œuf", 'Corail', 'Cramoisi', 'Cuisse de nymphe', 'Cuivre',
        'Cyan', 'Écarlate', 'Écru', 'Émeraude', 'Fauve', 'Flave', 'Fraise', 'Fraise écrasée', 'Framboise', 'Fuchsia', 'Fumée',
        'Garance (pigment)', 'Glauque', 'Glycine', 'Grège', 'Grenadine', 'Grenat', 'Gris acier', 'Gris de Payne', 'Gris fer',
        'Gris perle', 'Gris souris', 'Groseille', 'Gueules', 'Héliotrope', 'Incarnat', 'Indigo', 'Indigo', 'Isabelle',
        'Jaune canari', 'Jaune citron', "Jaune d'or", 'Jaune de cobalt', 'Jaune de Mars', 'Jaune de Naples', 'Jaune impérial',
        'Jaune mimosa', 'Lapis-lazuli', 'Lavallière', 'Lavande', 'Lie de vin', 'Lilas', 'Lime ou vert citron', 'Lin', 'Magenta',
        'Maïs', 'Malachite', 'Mandarine', 'Marron', 'Mastic', 'Mauve', 'Menthe', 'Moutarde', 'Nacarat', 'Nankin', 'Noisette',
        'Ocre', 'Ocre rouge', 'Olive', 'Or (couleur)', 'Orange brûlé', 'Orchidée', 'Orpiment (pigment)', 'Paille', 'Parme', "Pelure d'oignon",
        'Pervenche', 'Pistache', 'Poil de chameau', 'Ponceau', 'Pourpre (héraldique)', 'Prasin', 'Prune', 'Puce', 'Rose Mountbatten',
        'Rouge anglais', 'Rouge cardinal', 'Rouge cerise', "Rouge d'Andrinople", 'Rouge de Falun', 'Rouge feu', 'Rouge indien', 'Rouge tomette', 'Rouille',
        'Rubis', 'Sable', 'Sable (héraldique)', 'Safre', 'Sang de bœuf', 'Sanguine', 'Saphir', 'Sarcelle', 'Saumon', 'Sépia',
        'Sinople', 'Smalt', 'Soufre', 'Tabac', "Terre d'ombre", 'Tomate', 'Topaze', 'Tourterelle', 'Turquoise', 'Vanille',
        'Vermeil', 'Vermillon', 'Vert bouteille', 'Vert céladon', "Vert d'eau", 'Vert de chrome', 'Vert-de-gris', 'Vert de Hooker',
        'Vert de vessie', 'Vert épinard', 'Vert impérial', 'Vert lichen', 'Vert olive', 'Vert perroquet', 'Vert poireau',
        'Vert pomme', 'Vert prairie', 'Vert printemps', 'Vert sapin', 'Vert sauge', 'Vert tilleul', 'Vert Véronèse',
        'Violet', "Violet d'évêque", 'Viride', 'Zinzolin',
    ];
}
