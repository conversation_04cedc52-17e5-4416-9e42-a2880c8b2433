ERROR - 2025-07-31 14:01:23 --> <PERSON>rror connecting to the database: mysqli_sql_exception: Unknown database 'ci4_test' in I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'ci4_test', 3306, '', 0)
#1 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\DatabaseTestTrait.php(83): CodeIgniter\Database\BaseConnection->initialize()
#3 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\DatabaseTestTrait.php(59): ProductSalePriceTest->loadDependencies()
#4 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\CIUnitTestCase.php(251): ProductSalePriceTest->setUpDatabase()
#5 I:\xampp\htdocs\nandinihub\tests\ProductSalePriceTest.php(22): CodeIgniter\Test\CIUnitTestCase->setUp()
#6 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(2179): ProductSalePriceTest->setUp()
#7 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(2089): PHPUnit\Framework\TestCase->invokeHookMethods(Array, Object(PHPUnit\Event\DispatchingEmitter), 'beforeTestMetho...', 'beforeTestMetho...', 'beforeTestMetho...')
#8 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(678): PHPUnit\Framework\TestCase->invokeBeforeTestHookMethods(Array, Object(PHPUnit\Event\DispatchingEmitter))
#9 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestRunner.php(106): PHPUnit\Framework\TestCase->runBare()
#10 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(516): PHPUnit\Framework\TestRunner->run(Object(ProductSalePriceTest))
#11 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestSuite.php(374): PHPUnit\Framework\TestCase->run()
#12 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\TextUI\TestRunner.php(64): PHPUnit\Framework\TestSuite->run()
#13 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\TextUI\Application.php(203): PHPUnit\TextUI\TestRunner->run(Object(PHPUnit\TextUI\Configuration\Configuration), Object(PHPUnit\Runner\ResultCache\DefaultResultCache), Object(PHPUnit\Framework\TestSuite))
#14 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\phpunit(104): PHPUnit\TextUI\Application->run(Array)
#15 I:\xampp\htdocs\nandinihub\vendor\bin\phpunit(122): include('I:\\xampp\\htdocs...')
#16 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'ci4_test' in I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\DatabaseTestTrait.php(83): CodeIgniter\Database\BaseConnection->initialize()
#2 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\DatabaseTestTrait.php(59): ProductSalePriceTest->loadDependencies()
#3 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\CIUnitTestCase.php(251): ProductSalePriceTest->setUpDatabase()
#4 I:\xampp\htdocs\nandinihub\tests\ProductSalePriceTest.php(22): CodeIgniter\Test\CIUnitTestCase->setUp()
#5 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(2179): ProductSalePriceTest->setUp()
#6 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(2089): PHPUnit\Framework\TestCase->invokeHookMethods(Array, Object(PHPUnit\Event\DispatchingEmitter), 'beforeTestMetho...', 'beforeTestMetho...', 'beforeTestMetho...')
#7 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(678): PHPUnit\Framework\TestCase->invokeBeforeTestHookMethods(Array, Object(PHPUnit\Event\DispatchingEmitter))
#8 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestRunner.php(106): PHPUnit\Framework\TestCase->runBare()
#9 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(516): PHPUnit\Framework\TestRunner->run(Object(ProductSalePriceTest))
#10 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestSuite.php(374): PHPUnit\Framework\TestCase->run()
#11 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\TextUI\TestRunner.php(64): PHPUnit\Framework\TestSuite->run()
#12 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\TextUI\Application.php(203): PHPUnit\TextUI\TestRunner->run(Object(PHPUnit\TextUI\Configuration\Configuration), Object(PHPUnit\Runner\ResultCache\DefaultResultCache), Object(PHPUnit\Framework\TestSuite))
#13 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\phpunit(104): PHPUnit\TextUI\Application->run(Array)
#14 I:\xampp\htdocs\nandinihub\vendor\bin\phpunit(122): include('I:\\xampp\\htdocs...')
#15 {main}
ERROR - 2025-07-31 14:01:23 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'ci4_test' in I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'ci4_test', 3306, '', 0)
#1 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\DatabaseTestTrait.php(83): CodeIgniter\Database\BaseConnection->initialize()
#3 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\DatabaseTestTrait.php(59): ProductSalePriceTest->loadDependencies()
#4 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\CIUnitTestCase.php(251): ProductSalePriceTest->setUpDatabase()
#5 I:\xampp\htdocs\nandinihub\tests\ProductSalePriceTest.php(22): CodeIgniter\Test\CIUnitTestCase->setUp()
#6 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(2179): ProductSalePriceTest->setUp()
#7 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(2089): PHPUnit\Framework\TestCase->invokeHookMethods(Array, Object(PHPUnit\Event\DispatchingEmitter), 'beforeTestMetho...', 'beforeTestMetho...', 'beforeTestMetho...')
#8 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(678): PHPUnit\Framework\TestCase->invokeBeforeTestHookMethods(Array, Object(PHPUnit\Event\DispatchingEmitter))
#9 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestRunner.php(106): PHPUnit\Framework\TestCase->runBare()
#10 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(516): PHPUnit\Framework\TestRunner->run(Object(ProductSalePriceTest))
#11 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestSuite.php(374): PHPUnit\Framework\TestCase->run()
#12 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\TextUI\TestRunner.php(64): PHPUnit\Framework\TestSuite->run()
#13 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\TextUI\Application.php(203): PHPUnit\TextUI\TestRunner->run(Object(PHPUnit\TextUI\Configuration\Configuration), Object(PHPUnit\Runner\ResultCache\DefaultResultCache), Object(PHPUnit\Framework\TestSuite))
#14 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\phpunit(104): PHPUnit\TextUI\Application->run(Array)
#15 I:\xampp\htdocs\nandinihub\vendor\bin\phpunit(122): include('I:\\xampp\\htdocs...')
#16 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'ci4_test' in I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\DatabaseTestTrait.php(83): CodeIgniter\Database\BaseConnection->initialize()
#2 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\DatabaseTestTrait.php(59): ProductSalePriceTest->loadDependencies()
#3 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\CIUnitTestCase.php(251): ProductSalePriceTest->setUpDatabase()
#4 I:\xampp\htdocs\nandinihub\tests\ProductSalePriceTest.php(22): CodeIgniter\Test\CIUnitTestCase->setUp()
#5 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(2179): ProductSalePriceTest->setUp()
#6 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(2089): PHPUnit\Framework\TestCase->invokeHookMethods(Array, Object(PHPUnit\Event\DispatchingEmitter), 'beforeTestMetho...', 'beforeTestMetho...', 'beforeTestMetho...')
#7 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(678): PHPUnit\Framework\TestCase->invokeBeforeTestHookMethods(Array, Object(PHPUnit\Event\DispatchingEmitter))
#8 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestRunner.php(106): PHPUnit\Framework\TestCase->runBare()
#9 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(516): PHPUnit\Framework\TestRunner->run(Object(ProductSalePriceTest))
#10 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestSuite.php(374): PHPUnit\Framework\TestCase->run()
#11 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\TextUI\TestRunner.php(64): PHPUnit\Framework\TestSuite->run()
#12 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\TextUI\Application.php(203): PHPUnit\TextUI\TestRunner->run(Object(PHPUnit\TextUI\Configuration\Configuration), Object(PHPUnit\Runner\ResultCache\DefaultResultCache), Object(PHPUnit\Framework\TestSuite))
#13 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\phpunit(104): PHPUnit\TextUI\Application->run(Array)
#14 I:\xampp\htdocs\nandinihub\vendor\bin\phpunit(122): include('I:\\xampp\\htdocs...')
#15 {main}
ERROR - 2025-07-31 14:01:23 --> Error connecting to the database: mysqli_sql_exception: Unknown database 'ci4_test' in I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'ci4_test', 3306, '', 0)
#1 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\DatabaseTestTrait.php(83): CodeIgniter\Database\BaseConnection->initialize()
#3 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\DatabaseTestTrait.php(59): ProductSalePriceTest->loadDependencies()
#4 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\CIUnitTestCase.php(251): ProductSalePriceTest->setUpDatabase()
#5 I:\xampp\htdocs\nandinihub\tests\ProductSalePriceTest.php(22): CodeIgniter\Test\CIUnitTestCase->setUp()
#6 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(2179): ProductSalePriceTest->setUp()
#7 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(2089): PHPUnit\Framework\TestCase->invokeHookMethods(Array, Object(PHPUnit\Event\DispatchingEmitter), 'beforeTestMetho...', 'beforeTestMetho...', 'beforeTestMetho...')
#8 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(678): PHPUnit\Framework\TestCase->invokeBeforeTestHookMethods(Array, Object(PHPUnit\Event\DispatchingEmitter))
#9 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestRunner.php(106): PHPUnit\Framework\TestCase->runBare()
#10 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(516): PHPUnit\Framework\TestRunner->run(Object(ProductSalePriceTest))
#11 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestSuite.php(374): PHPUnit\Framework\TestCase->run()
#12 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\TextUI\TestRunner.php(64): PHPUnit\Framework\TestSuite->run()
#13 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\TextUI\Application.php(203): PHPUnit\TextUI\TestRunner->run(Object(PHPUnit\TextUI\Configuration\Configuration), Object(PHPUnit\Runner\ResultCache\DefaultResultCache), Object(PHPUnit\Framework\TestSuite))
#14 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\phpunit(104): PHPUnit\TextUI\Application->run(Array)
#15 I:\xampp\htdocs\nandinihub\vendor\bin\phpunit(122): include('I:\\xampp\\htdocs...')
#16 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Unknown database 'ci4_test' in I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\DatabaseTestTrait.php(83): CodeIgniter\Database\BaseConnection->initialize()
#2 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\DatabaseTestTrait.php(59): ProductSalePriceTest->loadDependencies()
#3 I:\xampp\htdocs\nandinihub\vendor\codeigniter4\framework\system\Test\CIUnitTestCase.php(251): ProductSalePriceTest->setUpDatabase()
#4 I:\xampp\htdocs\nandinihub\tests\ProductSalePriceTest.php(22): CodeIgniter\Test\CIUnitTestCase->setUp()
#5 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(2179): ProductSalePriceTest->setUp()
#6 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(2089): PHPUnit\Framework\TestCase->invokeHookMethods(Array, Object(PHPUnit\Event\DispatchingEmitter), 'beforeTestMetho...', 'beforeTestMetho...', 'beforeTestMetho...')
#7 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(678): PHPUnit\Framework\TestCase->invokeBeforeTestHookMethods(Array, Object(PHPUnit\Event\DispatchingEmitter))
#8 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestRunner.php(106): PHPUnit\Framework\TestCase->runBare()
#9 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestCase.php(516): PHPUnit\Framework\TestRunner->run(Object(ProductSalePriceTest))
#10 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\Framework\TestSuite.php(374): PHPUnit\Framework\TestCase->run()
#11 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\TextUI\TestRunner.php(64): PHPUnit\Framework\TestSuite->run()
#12 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\src\TextUI\Application.php(203): PHPUnit\TextUI\TestRunner->run(Object(PHPUnit\TextUI\Configuration\Configuration), Object(PHPUnit\Runner\ResultCache\DefaultResultCache), Object(PHPUnit\Framework\TestSuite))
#13 I:\xampp\htdocs\nandinihub\vendor\phpunit\phpunit\phpunit(104): PHPUnit\TextUI\Application->run(Array)
#14 I:\xampp\htdocs\nandinihub\vendor\bin\phpunit(122): include('I:\\xampp\\htdocs...')
#15 {main}
